"""关键词分析服务 - 集成多个数据源进行关键词分析"""

import asyncio
import httpx
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class KeywordMetrics:
    """关键词指标数据类"""
    keyword: str
    search_volume: Optional[int] = None
    keyword_difficulty: Optional[float] = None
    cpc: Optional[float] = None
    competition: Optional[float] = None
    search_intent: Optional[str] = None
    zero_click_rate: Optional[float] = None
    trend_score: Optional[float] = None
    trend_data: Optional[Dict[str, Any]] = None
    serp_quality_score: Optional[float] = None
    serp_analysis: Optional[Dict[str, Any]] = None


class SEMrushAPI:
    """SEMrush API 集成"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.semrush.com"
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get_keyword_overview(self, keyword: str, database: str = "us") -> Dict[str, Any]:
        """获取关键词概览数据"""
        try:
            params = {
                "type": "phrase_this",
                "key": self.api_key,
                "phrase": keyword,
                "database": database,
                "export_columns": "Ph,Nq,Cp,Co,Nr,Td"
            }
            
            response = await self.client.get(f"{self.base_url}/", params=params)
            response.raise_for_status()
            
            lines = response.text.strip().split('\n')
            if len(lines) < 2:
                return {}
            
            data = lines[1].split(';')
            if len(data) >= 6:
                return {
                    "keyword": data[0],
                    "search_volume": int(data[1]) if data[1].isdigit() else 0,
                    "cpc": float(data[2]) if data[2] else 0.0,
                    "competition": float(data[3]) if data[3] else 0.0,
                    "results_count": int(data[4]) if data[4].isdigit() else 0,
                    "trends": data[5] if len(data) > 5 else ""
                }
        except Exception as e:
            logger.error(f"SEMrush API error for keyword '{keyword}': {str(e)}")
            return {}
    
    async def get_keyword_difficulty(self, keyword: str, database: str = "us") -> float:
        """获取关键词难度"""
        try:
            params = {
                "type": "phrase_kdi",
                "key": self.api_key,
                "phrase": keyword,
                "database": database
            }
            
            response = await self.client.get(f"{self.base_url}/", params=params)
            response.raise_for_status()
            
            lines = response.text.strip().split('\n')
            if len(lines) >= 2:
                difficulty = lines[1].split(';')[1]
                return float(difficulty) if difficulty else 0.0
        except Exception as e:
            logger.error(f"SEMrush KD error for keyword '{keyword}': {str(e)}")
            return 0.0


class GoogleTrendsAPI:
    """Google Trends API 模拟 (实际使用pytrends库)"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get_trend_data(self, keyword: str, timeframe: str = "today 12-m") -> Dict[str, Any]:
        """获取趋势数据"""
        try:
            # 这里使用模拟数据，实际应该使用 pytrends 库
            # 由于 pytrends 是同步库，可以在异步函数中使用 asyncio.to_thread
            
            # 模拟趋势数据
            import random
            trend_values = [random.randint(20, 100) for _ in range(12)]
            
            # 计算趋势斜率
            if len(trend_values) >= 2:
                slope = (trend_values[-1] - trend_values[0]) / len(trend_values)
                if slope > 2:
                    trend_score = 1.0
                elif slope > -2:
                    trend_score = 0.5
                else:
                    trend_score = 0.0
            else:
                trend_score = 0.5
            
            return {
                "keyword": keyword,
                "trend_values": trend_values,
                "trend_score": trend_score,
                "timeframe": timeframe,
                "average_interest": sum(trend_values) / len(trend_values)
            }
        except Exception as e:
            logger.error(f"Google Trends error for keyword '{keyword}': {str(e)}")
            return {"trend_score": 0.5, "trend_values": []}


class SERPAnalyzer:
    """SERP 分析器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        )
    
    async def analyze_serp(self, keyword: str) -> Dict[str, Any]:
        """分析搜索结果页面质量"""
        try:
            # 模拟 SERP 分析
            # 实际实现应该使用真实的搜索API或爬虫
            
            # 模拟检测到的网站类型
            site_types = ["tool", "article", "forum", "commercial", "wiki"]
            domain_strengths = ["high", "medium", "low"]
            content_ages = ["recent", "old", "very_old"]
            
            # 随机生成分析结果
            import random
            serp_results = []
            for i in range(10):
                result = {
                    "position": i + 1,
                    "domain": f"example{i}.com",
                    "site_type": random.choice(site_types),
                    "domain_strength": random.choice(domain_strengths),
                    "content_age": random.choice(content_ages)
                }
                serp_results.append(result)
            
            # 计算质量分数
            quality_score = self._calculate_serp_quality_score(serp_results)
            
            return {
                "keyword": keyword,
                "serp_results": serp_results,
                "quality_score": quality_score,
                "analysis_date": datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"SERP analysis error for keyword '{keyword}': {str(e)}")
            return {"quality_score": 0.5, "serp_results": []}
    
    def _calculate_serp_quality_score(self, serp_results: List[Dict]) -> float:
        """计算SERP质量分数"""
        score = 0.0
        
        for result in serp_results[:10]:  # 只看前10个结果
            # 如果发现UGC平台或老旧内容，增加机会分数
            if result["site_type"] in ["forum", "wiki"]:
                score += 0.1
            if result["content_age"] in ["old", "very_old"]:
                score += 0.08
            if result["domain_strength"] == "low":
                score += 0.05
        
        return min(score, 1.0)


class KeywordAnalyzer:
    """关键词分析器主类"""
    
    def __init__(self, semrush_api_key: Optional[str] = None):
        self.semrush = SEMrushAPI(semrush_api_key) if semrush_api_key else None
        self.google_trends = GoogleTrendsAPI()
        self.serp_analyzer = SERPAnalyzer()
    
    async def analyze_keyword(self, keyword: str, language: str = "en", country: str = "us") -> KeywordMetrics:
        """分析单个关键词"""
        
        # 并发执行多个分析任务
        tasks = []
        
        # SEMrush 数据
        if self.semrush:
            tasks.append(self.semrush.get_keyword_overview(keyword, country))
            tasks.append(self.semrush.get_keyword_difficulty(keyword, country))
        
        # Google Trends 数据
        tasks.append(self.google_trends.get_trend_data(keyword))
        
        # SERP 分析
        tasks.append(self.serp_analyzer.analyze_serp(keyword))
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 解析结果
            semrush_overview = results[0] if len(results) > 0 and isinstance(results[0], dict) else {}
            semrush_kd = results[1] if len(results) > 1 and isinstance(results[1], (int, float)) else 0.0
            trends_data = results[2] if len(results) > 2 and isinstance(results[2], dict) else {}
            serp_data = results[3] if len(results) > 3 and isinstance(results[3], dict) else {}
            
            # 构建关键词指标
            metrics = KeywordMetrics(
                keyword=keyword,
                search_volume=semrush_overview.get("search_volume"),
                keyword_difficulty=semrush_kd,
                cpc=semrush_overview.get("cpc"),
                competition=semrush_overview.get("competition"),
                search_intent=self._determine_search_intent(keyword),
                trend_score=trends_data.get("trend_score"),
                trend_data=trends_data,
                serp_quality_score=serp_data.get("quality_score"),
                serp_analysis=serp_data
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error analyzing keyword '{keyword}': {str(e)}")
            return KeywordMetrics(keyword=keyword)
    
    async def analyze_keywords_batch(self, keywords: List[str], language: str = "en", country: str = "us") -> List[KeywordMetrics]:
        """批量分析关键词"""
        
        # 限制并发数量以避免API限制
        semaphore = asyncio.Semaphore(5)
        
        async def analyze_with_semaphore(keyword):
            async with semaphore:
                return await self.analyze_keyword(keyword, language, country)
        
        tasks = [analyze_with_semaphore(keyword) for keyword in keywords]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, KeywordMetrics):
                valid_results.append(result)
            else:
                logger.error(f"Error in batch analysis: {result}")
        
        return valid_results
    
    def _determine_search_intent(self, keyword: str) -> str:
        """确定搜索意图"""
        keyword_lower = keyword.lower()
        
        # 交易型关键词
        if any(word in keyword_lower for word in ["buy", "purchase", "price", "cost", "cheap", "best", "review"]):
            return "transactional"
        
        # 商业型关键词
        elif any(word in keyword_lower for word in ["compare", "vs", "alternative", "tool", "software", "app"]):
            return "commercial"
        
        # 信息型关键词
        elif any(word in keyword_lower for word in ["how", "what", "why", "when", "where", "guide", "tutorial"]):
            return "informational"
        
        # 导航型关键词
        elif any(word in keyword_lower for word in ["login", "sign in", "website", "official", "homepage"]):
            return "navigational"
        
        # 默认为信息型
        return "informational"
    
    def calculate_potential_score(self, metrics: KeywordMetrics, weights: Optional[Dict[str, float]] = None) -> float:
        """计算关键词潜力分数"""
        
        if weights is None:
            weights = {
                "volume": 0.3,
                "difficulty": 0.3,
                "cpc": 0.2,
                "trend": 0.1,
                "serp_quality": 0.1
            }
        
        score = 0.0
        
        # 搜索量评分 (归一化到0-1)
        if metrics.search_volume is not None:
            volume_score = min(metrics.search_volume / 10000, 1.0)  # 假设10000为满分
            score += weights["volume"] * volume_score
        
        # 难度评分 (反向评分，难度越低分数越高)
        if metrics.keyword_difficulty is not None:
            difficulty_score = max(0, (100 - metrics.keyword_difficulty) / 100)
            score += weights["difficulty"] * difficulty_score
        
        # CPC评分 (归一化到0-1)
        if metrics.cpc is not None:
            cpc_score = min(metrics.cpc / 10, 1.0)  # 假设10美元为满分
            score += weights["cpc"] * cpc_score
        
        # 趋势评分
        if metrics.trend_score is not None:
            score += weights["trend"] * metrics.trend_score
        
        # SERP质量评分
        if metrics.serp_quality_score is not None:
            score += weights["serp_quality"] * metrics.serp_quality_score
        
        return min(score, 1.0)


class KeywordExpander:
    """关键词扩展器"""
    
    def __init__(self):
        self.common_modifiers = {
            "tools": ["online", "free", "best", "tool", "generator", "converter", "calculator"],
            "converters": ["online", "free", "to", "converter", "convert"],
            "generators": ["generator", "maker", "creator", "builder", "online", "free"],
            "calculators": ["calculator", "calculate", "estimation", "online", "free"]
        }
    
    def expand_seed_keywords(self, seed_keywords: List[str], category: str = "tools") -> List[str]:
        """扩展种子关键词"""
        expanded = []
        modifiers = self.common_modifiers.get(category, self.common_modifiers["tools"])
        
        for seed in seed_keywords:
            # 原始关键词
            expanded.append(seed)
            
            # 添加前缀修饰词
            for modifier in modifiers:
                if modifier not in seed.lower():
                    expanded.append(f"{modifier} {seed}")
                    expanded.append(f"{seed} {modifier}")
        
        # 去重并返回
        return list(set(expanded))
    
    def generate_keyword_combinations(self, base_words: List[str], modifiers: List[str]) -> List[str]:
        """生成关键词组合"""
        combinations = []
        
        for base in base_words:
            for modifier in modifiers:
                combinations.extend([
                    f"{modifier} {base}",
                    f"{base} {modifier}",
                    f"{modifier}{base}",  # 连写
                ])
        
        return list(set(combinations))