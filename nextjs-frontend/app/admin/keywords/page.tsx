"use client";

import { useState, useEffect } from "react";

interface Keyword {
  id: string;
  keyword: string;
  search_volume: number | null;
  keyword_difficulty: number | null;
  cpc: number | null;
  potential_score: number | null;
  status: string;
  is_approved: boolean;
  created_at: string;
  tool_category?: string;
}

interface KeywordFilters {
  status: string;
  approved: string;
  minScore: string;
  maxDifficulty: string;
  search: string;
}

export default function KeywordsPage() {
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
  const [filters, setFilters] = useState<KeywordFilters>({
    status: "all",
    approved: "all", 
    minScore: "",
    maxDifficulty: "",
    search: ""
  });

  useEffect(() => {
    fetchKeywords();
  }, [filters]);

  const fetchKeywords = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockKeywords: Keyword[] = [
        {
          id: "1",
          keyword: "online pdf converter",
          search_volume: 12500,
          keyword_difficulty: 45.2,
          cpc: 1.85,
          potential_score: 0.87,
          status: "evaluated",
          is_approved: true,
          created_at: "2024-01-15T10:30:00Z",
          tool_category: "converter"
        },
        {
          id: "2",
          keyword: "image resizer tool",
          search_volume: 8900,
          keyword_difficulty: 38.7,
          cpc: 1.42,
          potential_score: 0.76,
          status: "approved",
          is_approved: true,
          created_at: "2024-01-15T09:15:00Z",
          tool_category: "tool"
        },
        {
          id: "3",
          keyword: "text to speech generator",
          search_volume: 15200,
          keyword_difficulty: 52.3,
          cpc: 2.34,
          potential_score: 0.92,
          status: "evaluated",
          is_approved: false,
          created_at: "2024-01-14T16:45:00Z",
          tool_category: "generator"
        },
        {
          id: "4",
          keyword: "qr code maker",
          search_volume: 6750,
          keyword_difficulty: 32.1,
          cpc: 0.98,
          potential_score: 0.65,
          status: "enriched",
          is_approved: false,
          created_at: "2024-01-14T14:20:00Z",
          tool_category: "maker"
        },
        {
          id: "5",
          keyword: "password generator secure",
          search_volume: 9800,
          keyword_difficulty: 41.5,
          cpc: 1.67,
          potential_score: 0.83,
          status: "evaluated",
          is_approved: false,
          created_at: "2024-01-14T11:30:00Z",
          tool_category: "generator"
        }
      ];

      setKeywords(mockKeywords);
    } catch (error) {
      console.error("Failed to fetch keywords:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectKeyword = (keywordId: string) => {
    setSelectedKeywords(prev =>
      prev.includes(keywordId)
        ? prev.filter(id => id !== keywordId)
        : [...prev, keywordId]
    );
  };

  const handleSelectAll = () => {
    if (selectedKeywords.length === keywords.length) {
      setSelectedKeywords([]);
    } else {
      setSelectedKeywords(keywords.map(k => k.id));
    }
  };

  const handleApproveSelected = async () => {
    // 实现批量批准逻辑
    console.log("Approving keywords:", selectedKeywords);
  };

  const handleGenerateWebsites = async () => {
    // 实现批量生成网站逻辑
    console.log("Generating websites for keywords:", selectedKeywords);
  };

  const getScoreColor = (score: number | null) => {
    if (!score) return "text-gray-400";
    if (score >= 0.8) return "text-green-600";
    if (score >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "bg-green-100 text-green-800";
      case "evaluated": return "bg-blue-100 text-blue-800";
      case "enriched": return "bg-yellow-100 text-yellow-800";
      case "raw": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Keywords</h1>
          <p className="mt-2 text-gray-600">Manage and analyze your keyword database</p>
        </div>
        <div className="mt-4 sm:mt-0 sm:flex-none">
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
          >
            Add Keywords
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
            <div>
              <label className="block text-sm font-medium text-gray-700">Search</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Search keywords..."
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All Statuses</option>
                <option value="raw">Raw</option>
                <option value="enriched">Enriched</option>
                <option value="evaluated">Evaluated</option>
                <option value="approved">Approved</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Approved</label>
              <select
                value={filters.approved}
                onChange={(e) => setFilters(prev => ({ ...prev, approved: e.target.value }))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All</option>
                <option value="approved">Approved Only</option>
                <option value="pending">Pending Approval</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Min Score</label>
              <input
                type="number"
                step="0.1"
                min="0"
                max="1"
                value={filters.minScore}
                onChange={(e) => setFilters(prev => ({ ...prev, minScore: e.target.value }))}
                placeholder="0.0"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Max Difficulty</label>
              <input
                type="number"
                min="0"
                max="100"
                value={filters.maxDifficulty}
                onChange={(e) => setFilters(prev => ({ ...prev, maxDifficulty: e.target.value }))}
                placeholder="100"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedKeywords.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm text-blue-700">
                {selectedKeywords.length} keyword(s) selected
              </span>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleApproveSelected}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Approve Selected
              </button>
              <button
                onClick={handleGenerateWebsites}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Generate Websites
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Keywords Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Keywords ({keywords.length})</h3>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedKeywords.length === keywords.length}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Keyword
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Difficulty
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    CPC
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Potential Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {keywords.map((keyword) => (
                  <tr key={keyword.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedKeywords.includes(keyword.id)}
                        onChange={() => handleSelectKeyword(keyword.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm font-medium text-gray-900">{keyword.keyword}</div>
                        {keyword.tool_category && (
                          <div className="text-sm text-gray-500">{keyword.tool_category}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.search_volume ? keyword.search_volume.toLocaleString() : "—"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.keyword_difficulty ? `${keyword.keyword_difficulty.toFixed(1)}%` : "—"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.cpc ? `$${keyword.cpc.toFixed(2)}` : "—"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${getScoreColor(keyword.potential_score)}`}>
                        {keyword.potential_score ? (keyword.potential_score * 100).toFixed(1) + "%" : "—"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(keyword.status)}`}>
                        {keyword.status}
                      </span>
                      {keyword.is_approved && (
                        <span className="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          ✓
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          View
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          Approve
                        </button>
                        <button className="text-purple-600 hover:text-purple-900">
                          Generate
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}