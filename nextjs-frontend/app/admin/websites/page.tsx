"use client";

import { useState, useEffect } from "react";

interface Website {
  id: string;
  title: string;
  keyword: string;
  status: string;
  template_id?: string;
  deployment_url?: string;
  github_repo?: string;
  created_at: string;
  deployed_at?: string;
  lighthouse_score?: {
    performance: number;
    accessibility: number;
    bestPractices: number;
    seo: number;
  };
}

interface WebsiteFilters {
  status: string;
  template: string;
  search: string;
}

export default function WebsitesPage() {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWebsites, setSelectedWebsites] = useState<string[]>([]);
  const [filters, setFilters] = useState<WebsiteFilters>({
    status: "all",
    template: "all",
    search: ""
  });

  useEffect(() => {
    fetchWebsites();
  }, [filters]);

  const fetchWebsites = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockWebsites: Website[] = [
        {
          id: "1",
          title: "PDF Converter Tool",
          keyword: "online pdf converter",
          status: "deployed",
          template_id: "converter-basic",
          deployment_url: "https://pdf-converter-tool.vercel.app",
          github_repo: "https://github.com/username/pdf-converter-tool",
          created_at: "2024-01-15T11:00:00Z",
          deployed_at: "2024-01-15T11:15:00Z",
          lighthouse_score: {
            performance: 95,
            accessibility: 98,
            bestPractices: 92,
            seo: 100
          }
        },
        {
          id: "2",
          title: "Image Resizer",
          keyword: "image resizer tool",
          status: "building",
          template_id: "converter-basic",
          github_repo: "https://github.com/username/image-resizer",
          created_at: "2024-01-15T10:45:00Z"
        },
        {
          id: "3",
          title: "Text to Speech Generator",
          keyword: "text to speech generator",
          status: "deployed",
          template_id: "generator-standard",
          deployment_url: "https://text-to-speech-gen.vercel.app",
          github_repo: "https://github.com/username/text-to-speech-gen",
          created_at: "2024-01-14T16:45:00Z",
          deployed_at: "2024-01-14T17:02:00Z",
          lighthouse_score: {
            performance: 89,
            accessibility: 94,
            bestPractices: 88,
            seo: 96
          }
        },
        {
          id: "4",
          title: "QR Code Maker",
          keyword: "qr code maker",
          status: "failed",
          template_id: "generator-standard",
          created_at: "2024-01-14T14:20:00Z"
        },
        {
          id: "5",
          title: "Password Generator",
          keyword: "password generator secure",
          status: "pending",
          created_at: "2024-01-14T11:30:00Z"
        }
      ];

      setWebsites(mockWebsites);
    } catch (error) {
      console.error("Failed to fetch websites:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectWebsite = (websiteId: string) => {
    setSelectedWebsites(prev =>
      prev.includes(websiteId)
        ? prev.filter(id => id !== websiteId)
        : [...prev, websiteId]
    );
  };

  const handleSelectAll = () => {
    if (selectedWebsites.length === websites.length) {
      setSelectedWebsites([]);
    } else {
      setSelectedWebsites(websites.map(w => w.id));
    }
  };

  const handleBatchDeploy = async () => {
    console.log("Deploying websites:", selectedWebsites);
  };

  const handleBatchDelete = async () => {
    console.log("Deleting websites:", selectedWebsites);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "deployed": return "bg-green-100 text-green-800";
      case "building": return "bg-yellow-100 text-yellow-800";
      case "pending": return "bg-gray-100 text-gray-800";
      case "failed": return "bg-red-100 text-red-800";
      case "deploying": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getLighthouseScore = (score?: number) => {
    if (!score) return { color: "text-gray-400", label: "—" };
    if (score >= 90) return { color: "text-green-600", label: score.toString() };
    if (score >= 70) return { color: "text-yellow-600", label: score.toString() };
    return { color: "text-red-600", label: score.toString() };
  };

  return (
    <div className="space-y-6">
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Websites</h1>
          <p className="mt-2 text-gray-600">Manage generated websites and deployments</p>
        </div>
        <div className="mt-4 sm:mt-0 sm:flex-none">
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
          >
            Generate Website
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">Search</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Search websites..."
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="building">Building</option>
                <option value="deploying">Deploying</option>
                <option value="deployed">Deployed</option>
                <option value="failed">Failed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Template</label>
              <select
                value={filters.template}
                onChange={(e) => setFilters(prev => ({ ...prev, template: e.target.value }))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="all">All Templates</option>
                <option value="converter-basic">Converter Basic</option>
                <option value="generator-standard">Generator Standard</option>
                <option value="calculator-advanced">Calculator Advanced</option>
                <option value="universal-tool">Universal Tool</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedWebsites.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm text-blue-700">
                {selectedWebsites.length} website(s) selected
              </span>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleBatchDeploy}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Deploy Selected
              </button>
              <button
                onClick={handleBatchDelete}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Websites Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Websites ({websites.length})</h3>
        </div>
        
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedWebsites.length === websites.length}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Website
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Template
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lighthouse Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deployment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {websites.map((website) => (
                  <tr key={website.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedWebsites.includes(website.id)}
                        onChange={() => handleSelectWebsite(website.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm font-medium text-gray-900">{website.title}</div>
                        <div className="text-sm text-gray-500">{website.keyword}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(website.status)}`}>
                        {website.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {website.template_id || "—"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {website.lighthouse_score ? (
                        <div className="grid grid-cols-2 gap-1 text-xs">
                          <div className="flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                            <span className={getLighthouseScore(website.lighthouse_score.performance).color}>
                              {getLighthouseScore(website.lighthouse_score.performance).label}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                            <span className={getLighthouseScore(website.lighthouse_score.accessibility).color}>
                              {getLighthouseScore(website.lighthouse_score.accessibility).label}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></span>
                            <span className={getLighthouseScore(website.lighthouse_score.bestPractices).color}>
                              {getLighthouseScore(website.lighthouse_score.bestPractices).label}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-1"></span>
                            <span className={getLighthouseScore(website.lighthouse_score.seo).color}>
                              {getLighthouseScore(website.lighthouse_score.seo).label}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">—</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        {website.deployment_url && (
                          <a
                            href={website.deployment_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 truncate max-w-32"
                          >
                            Live Site
                          </a>
                        )}
                        {website.github_repo && (
                          <a
                            href={website.github_repo}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-gray-600 hover:text-gray-800 truncate max-w-32"
                          >
                            GitHub
                          </a>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          View
                        </button>
                        {website.status === "failed" && (
                          <button className="text-green-600 hover:text-green-900">
                            Retry
                          </button>
                        )}
                        {website.status === "deployed" && (
                          <button className="text-purple-600 hover:text-purple-900">
                            Redeploy
                          </button>
                        )}
                        <button className="text-red-600 hover:text-red-900">
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}