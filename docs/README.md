## Next.js FastAPI Template

<a href="https://www.vintasoftware.com/blog/next-js-fastapi-template"><img src="images/banner.png" alt="Next.js FastAPI Template" width="auto"></a>
<p align="center">
    <em>Next.js FastAPI Template: Python + Modern TypeScript stack with Zod validation.</em>
</p>
<p align="center">
<a href="https://github.com/vintasoftware/nextjs-fastapi-template/actions/workflows/ci.yml" target="_blank">
    <img src="https://github.com/vintasoftware/nextjs-fastapi-template/actions/workflows/ci.yml/badge.svg" alt="CI">
</a>
<a href="https://coveralls.io/github/vintasoftware/nextjs-fastapi-template" target="_blank">
    <img src="https://coveralls.io/repos/github/vintasoftware/nextjs-fastapi-template/badge.svg" alt="Coverage">
</a>
</p>

---

**Documentation**: <a href="https://vintasoftware.github.io/nextjs-fastapi-template/" target="_blank">https://vintasoftware.github.io/nextjs-fastapi-template/</a>

**Source Code**: <a href="https://github.com/vintasoftware/nextjs-fastapi-template/" target="_blank">https://github.com/vintasoftware/nextjs-fastapi-template/</a>

---

The Next.js FastAPI Template provides a solid foundation for scalable, high-performance web applications, following clean architecture and best practices. It simplifies development by integrating FastAPI, Pydantic, and Next.js with TypeScript and Zod, ensuring end-to-end type safety and schema validation between frontend and backend.

The FastAPI backend supports fully asynchronous operations, optimizing database queries, API routes, and test execution for better performance. Deployment is seamless, with both backend and frontend fully deployable to Vercel, enabling quick product releases with minimal configuration.

### Key features
✔ End-to-end type safety – Automatically generated typed clients from the OpenAPI schema ensure seamless API contracts between frontend and backend.

✔ Hot-reload updates – The client updates automatically when backend routes change, keeping FastAPI and Next.js in sync.

✔ Versatile foundation – Designed for MVPs and production-ready applications, with a pre-configured authentication system and API layer.

✔ Quick deployment – Deploys a full-stack application—including authentication flow and a dashboard—on Vercel in just a few steps.

✔ Production-ready authentication – Includes a pre-configured authentication system and dashboard interface, allowing you to immediately start development with user management features.

## Technology stack
This template features a carefully selected set of technologies to ensure efficiency, scalability, and ease of use:

- Zod + TypeScript – Type safety and schema validation across the stack.
- fastapi-users – Complete authentication system with:
    - Secure password hashing
    - JWT authentication
- Email-based password recovery
- shadcn/ui – Prebuilt React components with Tailwind CSS.
- OpenAPI-fetch – Fully typed client generation from the OpenAPI schema.
- UV – Simplified dependency management and packaging.
- Docker Compose – Consistent environments for development and production.
- Pre-commit hooks – Automated code linting, formatting, and validation before commits.
- Vercel Deployment – Serverless backend and scalable frontend, deployable with minimal configuration.

This is a partial list of the technologies included in the template. For a complete overview, visit our [Technology selection](https://vintasoftware.github.io/nextjs-fastapi-template/technology-selection/) page.

## Get Started

To use this template, visit our [Get Started](https://vintasoftware.github.io/nextjs-fastapi-template/get-started/) and follow the steps.

## Using the template? Let's talk!

We’re always curious to see how the community builds on top of it and where it’s being used. To collaborate:

- Join the conversation on [GitHub Discussions](https://github.com/vintasoftware/nextjs-fastapi-template/discussions)
- Report bugs or suggest improvements via [issues](https://github.com/vintasoftware/nextjs-fastapi-template/issues)
- Check the [Contributing](https://vintasoftware.github.io/nextjs-fastapi-template/contributing/) guide to get involved

This project is maintained by [Vinta Software](https://www.vinta.com.br/) and is actively used in production systems we build for clients. Talk to our expert consultants — get a free technical review: <EMAIL>.

*Disclaimer: This project is not affiliated with Vercel.*
